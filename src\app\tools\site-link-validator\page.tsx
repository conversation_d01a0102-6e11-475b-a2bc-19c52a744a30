import { Metadata } from 'next';
import SiteLinkValidator from '@/components/tools/SiteLinkValidator';
import { getToolBySlug } from '@/lib/tools';
import { generateToolMetadata } from '@/lib/seo-utils';

const toolSlug = 'site-link-validator';
const tool = getToolBySlug(toolSlug);

export const metadata: Metadata = generateToolMetadata({
  title: 'فحص روابط الموقع - تحقق من جميع الروابط الداخلية تلقائياً',
  description: 'أداة مجانية لفحص جميع الروابط الداخلية في الموقع تلقائياً للتأكد من عملها بشكل صحيح. تحسين SEO وجودة الموقع.',
  keywords: [
    'فحص روابط الموقع',
    'site link validator',
    'فحص الروابط الداخلية',
    'SEO',
    'تحسين المواقع',
    'جودة الموقع',
    'فحص تلقائي',
    'أدوات المطورين',
    'تحليل الموقع',
    'صيانة الموقع'
  ],
  toolSlug,
  category: 'أدوات المطورين'
});

export default function SiteLinkValidatorPage() {
  if (!tool) {
    return <div>الأداة غير موجودة</div>;
  }

  return (
    <div className="container mx-auto py-8">
      <div className="mb-8 text-center">
        <h1 className="text-3xl font-bold mb-4">{tool.name}</h1>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          {tool.description}
        </p>
      </div>
      
      <SiteLinkValidator />
      
      <div className="mt-12 space-y-8">
        <section className="prose prose-lg max-w-4xl mx-auto">
          <h2>ما هو فحص روابط الموقع؟</h2>
          <p>
            فحص روابط الموقع هو أداة تلقائية تقوم بفحص جميع الروابط الداخلية في موقعك الإلكتروني 
            للتأكد من عملها بشكل صحيح. هذه الأداة مصممة خصيصاً لهذا الموقع وتفحص جميع الصفحات والأدوات والأقسام.
          </p>

          <h3>ما يتم فحصه في الموقع</h3>
          <ul>
            <li><strong>الصفحات الرئيسية:</strong> الصفحة الرئيسية، المقالات، خريطة الموقع</li>
            <li><strong>صفحات الأقسام:</strong> جميع أقسام الأدوات المختلفة</li>
            <li><strong>صفحات الأدوات:</strong> جميع الأدوات والحاسبات المتوفرة</li>
            <li><strong>الصفحات الثابتة:</strong> حول الموقع، اتصل بنا، الأسئلة الشائعة</li>
            <li><strong>صفحات المقالات:</strong> عينة من المقالات المهمة</li>
            <li><strong>صفحات السنوات:</strong> صفحات السنوات الميلادية والهجرية</li>
          </ul>

          <h3>مميزات الأداة</h3>
          <ul>
            <li><strong>فحص تلقائي:</strong> لا حاجة لإدخال الروابط يدوياً</li>
            <li><strong>فحص شامل:</strong> تغطي جميع أجزاء الموقع المهمة</li>
            <li><strong>تقرير مفصل:</strong> عرض حالة كل رابط مع التفاصيل</li>
            <li><strong>إحصائيات سريعة:</strong> ملخص سريع لحالة الموقع</li>
            <li><strong>تصدير النتائج:</strong> إمكانية تصدير الروابط المكسورة</li>
            <li><strong>وقت الاستجابة:</strong> قياس سرعة تحميل كل صفحة</li>
          </ul>

          <h3>كيفية قراءة النتائج</h3>
          <ul>
            <li><strong>يعمل (أخضر):</strong> الرابط يعمل بشكل طبيعي</li>
            <li><strong>مكسور (أحمر):</strong> الرابط لا يعمل ويحتاج إصلاح</li>
            <li><strong>إعادة توجيه (أصفر):</strong> الرابط يعيد التوجيه لصفحة أخرى</li>
            <li><strong>جاري الفحص (أزرق):</strong> الرابط قيد الفحص حالياً</li>
          </ul>

          <h3>أهمية فحص الروابط الداخلية</h3>
          <ul>
            <li><strong>تحسين SEO:</strong> الروابط المكسورة تضر بترتيب الموقع</li>
            <li><strong>تجربة المستخدم:</strong> ضمان وصول الزوار لجميع الصفحات</li>
            <li><strong>الفهرسة:</strong> مساعدة محركات البحث في فهرسة الموقع</li>
            <li><strong>المصداقية:</strong> موقع بدون روابط مكسورة يبدو أكثر احترافية</li>
            <li><strong>التنقل:</strong> ضمان سهولة التنقل بين صفحات الموقع</li>
          </ul>

          <h3>متى يجب استخدام هذه الأداة؟</h3>
          <ul>
            <li>بعد إضافة أدوات أو صفحات جديدة</li>
            <li>بعد تحديث بنية الموقع أو الروابط</li>
            <li>بشكل دوري (شهرياً) للصيانة</li>
            <li>قبل إطلاق تحديثات كبيرة</li>
            <li>عند ملاحظة مشاكل في التنقل</li>
          </ul>

          <h3>نصائح للحفاظ على جودة الروابط</h3>
          <ul>
            <li>اختبر الروابط الجديدة قبل النشر</li>
            <li>استخدم روابط نسبية للصفحات الداخلية</li>
            <li>تحقق من الروابط بعد أي تغيير في بنية الموقع</li>
            <li>احتفظ بنسخة احتياطية قبل التحديثات الكبيرة</li>
            <li>راقب تقارير Google Search Console</li>
          </ul>
        </section>
      </div>
    </div>
  );
}

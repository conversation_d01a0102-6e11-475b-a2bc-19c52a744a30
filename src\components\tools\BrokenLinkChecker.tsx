'use client';

import React, { useState, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Search, 
  ExternalLink, 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  Download,
  Copy,
  RefreshCw,
  Globe,
  Link as LinkIcon
} from 'lucide-react';
import { toast } from 'sonner';

interface LinkResult {
  url: string;
  status: number | null;
  statusText: string;
  isWorking: boolean;
  responseTime: number;
  error?: string;
  redirectUrl?: string;
}

interface ScanResults {
  totalLinks: number;
  workingLinks: number;
  brokenLinks: number;
  redirects: number;
  results: LinkResult[];
  scanTime: number;
}

export default function BrokenLinkChecker() {
  const [url, setUrl] = useState('');
  const [bulkUrls, setBulkUrls] = useState('');
  const [isScanning, setIsScanning] = useState(false);
  const [progress, setProgress] = useState(0);
  const [results, setResults] = useState<ScanResults | null>(null);
  const [activeTab, setActiveTab] = useState('single');

  const checkSingleLink = async (linkUrl: string): Promise<LinkResult> => {
    const startTime = Date.now();
    
    try {
      // Use a CORS proxy for external links or fetch API for same-origin
      const response = await fetch(linkUrl, {
        method: 'HEAD',
        mode: 'no-cors', // This will limit what we can check but avoids CORS issues
      });
      
      const responseTime = Date.now() - startTime;
      
      return {
        url: linkUrl,
        status: response.status,
        statusText: response.statusText,
        isWorking: response.ok,
        responseTime,
        redirectUrl: response.redirected ? response.url : undefined
      };
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      return {
        url: linkUrl,
        status: null,
        statusText: 'Error',
        isWorking: false,
        responseTime,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  };

  const extractLinksFromPage = async (pageUrl: string): Promise<string[]> => {
    try {
      // For same-origin requests, we can extract links
      if (pageUrl.startsWith(window.location.origin) || pageUrl.startsWith('/')) {
        const response = await fetch(pageUrl);
        const html = await response.text();
        
        // Parse HTML and extract links
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');
        const links = Array.from(doc.querySelectorAll('a[href]'))
          .map(link => (link as HTMLAnchorElement).href)
          .filter(href => href && !href.startsWith('mailto:') && !href.startsWith('tel:'))
          .filter((href, index, array) => array.indexOf(href) === index); // Remove duplicates
        
        return links;
      } else {
        // For external URLs, we can't extract links due to CORS
        return [pageUrl];
      }
    } catch (error) {
      console.error('Error extracting links:', error);
      return [pageUrl];
    }
  };

  const scanWebsite = async () => {
    if (!url.trim()) {
      toast.error('يرجى إدخال رابط الموقع');
      return;
    }

    setIsScanning(true);
    setProgress(0);
    setResults(null);

    const startTime = Date.now();

    try {
      // Extract all links from the page
      const links = await extractLinksFromPage(url.trim());
      
      if (links.length === 0) {
        toast.error('لم يتم العثور على روابط في الصفحة');
        setIsScanning(false);
        return;
      }

      const results: LinkResult[] = [];
      
      // Check each link
      for (let i = 0; i < links.length; i++) {
        const link = links[i];
        setProgress((i / links.length) * 100);
        
        const result = await checkSingleLink(link);
        results.push(result);
        
        // Small delay to avoid overwhelming the server
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      const scanTime = Date.now() - startTime;
      const workingLinks = results.filter(r => r.isWorking).length;
      const brokenLinks = results.filter(r => !r.isWorking).length;
      const redirects = results.filter(r => r.redirectUrl).length;

      setResults({
        totalLinks: results.length,
        workingLinks,
        brokenLinks,
        redirects,
        results,
        scanTime
      });

      toast.success(`تم فحص ${results.length} رابط في ${(scanTime / 1000).toFixed(1)} ثانية`);
    } catch (error) {
      toast.error('حدث خطأ أثناء فحص الروابط');
      console.error('Scan error:', error);
    } finally {
      setIsScanning(false);
      setProgress(100);
    }
  };

  const scanBulkUrls = async () => {
    const urls = bulkUrls.split('\n').filter(u => u.trim());
    
    if (urls.length === 0) {
      toast.error('يرجى إدخال قائمة الروابط');
      return;
    }

    setIsScanning(true);
    setProgress(0);
    setResults(null);

    const startTime = Date.now();
    const results: LinkResult[] = [];

    try {
      for (let i = 0; i < urls.length; i++) {
        const url = urls[i].trim();
        setProgress((i / urls.length) * 100);
        
        const result = await checkSingleLink(url);
        results.push(result);
        
        // Small delay to avoid overwhelming servers
        await new Promise(resolve => setTimeout(resolve, 200));
      }

      const scanTime = Date.now() - startTime;
      const workingLinks = results.filter(r => r.isWorking).length;
      const brokenLinks = results.filter(r => !r.isWorking).length;
      const redirects = results.filter(r => r.redirectUrl).length;

      setResults({
        totalLinks: results.length,
        workingLinks,
        brokenLinks,
        redirects,
        results,
        scanTime
      });

      toast.success(`تم فحص ${results.length} رابط في ${(scanTime / 1000).toFixed(1)} ثانية`);
    } catch (error) {
      toast.error('حدث خطأ أثناء فحص الروابط');
      console.error('Bulk scan error:', error);
    } finally {
      setIsScanning(false);
      setProgress(100);
    }
  };

  const exportResults = () => {
    if (!results) return;

    const csvContent = [
      'URL,Status,Status Text,Response Time (ms),Working,Error,Redirect URL',
      ...results.results.map(r => 
        `"${r.url}",${r.status || 'N/A'},"${r.statusText}",${r.responseTime},${r.isWorking ? 'Yes' : 'No'},"${r.error || ''}","${r.redirectUrl || ''}"`
      )
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `broken-links-report-${new Date().toISOString().split('T')[0]}.csv`;
    link.click();
  };

  const copyResults = () => {
    if (!results) return;

    const text = results.results
      .filter(r => !r.isWorking)
      .map(r => `${r.url} - ${r.statusText} ${r.error ? `(${r.error})` : ''}`)
      .join('\n');

    navigator.clipboard.writeText(text);
    toast.success('تم نسخ الروابط المكسورة');
  };

  const getStatusIcon = (result: LinkResult) => {
    if (result.isWorking) {
      return <CheckCircle className="w-4 h-4 text-green-500" />;
    } else if (result.status && result.status >= 300 && result.status < 400) {
      return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
    } else {
      return <XCircle className="w-4 h-4 text-red-500" />;
    }
  };

  const getStatusBadge = (result: LinkResult) => {
    if (result.isWorking) {
      return <Badge variant="default" className="bg-green-100 text-green-800">يعمل</Badge>;
    } else if (result.status && result.status >= 300 && result.status < 400) {
      return <Badge variant="secondary">إعادة توجيه</Badge>;
    } else {
      return <Badge variant="destructive">مكسور</Badge>;
    }
  };

  return (
    <div className="w-full max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="w-5 h-5" />
            فاحص الروابط المكسورة
          </CardTitle>
          <CardDescription>
            اكتشف الروابط المكسورة في موقعك أو فحص قائمة من الروابط للتأكد من عملها
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="single">فحص موقع واحد</TabsTrigger>
              <TabsTrigger value="bulk">فحص متعدد</TabsTrigger>
            </TabsList>
            
            <TabsContent value="single" className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="website-url">رابط الموقع</Label>
                <Input
                  id="website-url"
                  type="url"
                  placeholder="https://example.com"
                  value={url}
                  onChange={(e) => setUrl(e.target.value)}
                  disabled={isScanning}
                />
              </div>
              <Button 
                onClick={scanWebsite} 
                disabled={isScanning || !url.trim()}
                className="w-full"
              >
                {isScanning ? (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                    جاري الفحص...
                  </>
                ) : (
                  <>
                    <Globe className="w-4 h-4 mr-2" />
                    فحص الموقع
                  </>
                )}
              </Button>
            </TabsContent>
            
            <TabsContent value="bulk" className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="bulk-urls">قائمة الروابط (رابط واحد في كل سطر)</Label>
                <Textarea
                  id="bulk-urls"
                  placeholder="https://example1.com&#10;https://example2.com&#10;https://example3.com"
                  value={bulkUrls}
                  onChange={(e) => setBulkUrls(e.target.value)}
                  disabled={isScanning}
                  rows={6}
                />
              </div>
              <Button 
                onClick={scanBulkUrls} 
                disabled={isScanning || !bulkUrls.trim()}
                className="w-full"
              >
                {isScanning ? (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                    جاري الفحص...
                  </>
                ) : (
                  <>
                    <LinkIcon className="w-4 h-4 mr-2" />
                    فحص الروابط
                  </>
                )}
              </Button>
            </TabsContent>
          </Tabs>

          {isScanning && (
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>جاري الفحص...</span>
                <span>{Math.round(progress)}%</span>
              </div>
              <Progress value={progress} className="w-full" />
            </div>
          )}
        </CardContent>
      </Card>

      {results && (
        <Card>
          <CardHeader>
            <CardTitle>نتائج الفحص</CardTitle>
            <CardDescription>
              تم فحص {results.totalLinks} رابط في {(results.scanTime / 1000).toFixed(1)} ثانية
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Summary */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">{results.totalLinks}</div>
                <div className="text-sm text-blue-600">إجمالي الروابط</div>
              </div>
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">{results.workingLinks}</div>
                <div className="text-sm text-green-600">روابط تعمل</div>
              </div>
              <div className="text-center p-4 bg-red-50 rounded-lg">
                <div className="text-2xl font-bold text-red-600">{results.brokenLinks}</div>
                <div className="text-sm text-red-600">روابط مكسورة</div>
              </div>
              <div className="text-center p-4 bg-yellow-50 rounded-lg">
                <div className="text-2xl font-bold text-yellow-600">{results.redirects}</div>
                <div className="text-sm text-yellow-600">إعادة توجيه</div>
              </div>
            </div>

            {/* Export buttons */}
            <div className="flex gap-2">
              <Button variant="outline" onClick={exportResults}>
                <Download className="w-4 h-4 mr-2" />
                تصدير CSV
              </Button>
              <Button variant="outline" onClick={copyResults}>
                <Copy className="w-4 h-4 mr-2" />
                نسخ الروابط المكسورة
              </Button>
            </div>

            {/* Results list */}
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {results.results.map((result, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center gap-3 flex-1 min-w-0">
                    {getStatusIcon(result)}
                    <div className="flex-1 min-w-0">
                      <div className="font-medium truncate">{result.url}</div>
                      <div className="text-sm text-muted-foreground">
                        {result.status ? `${result.status} - ${result.statusText}` : result.error}
                        {result.responseTime && ` (${result.responseTime}ms)`}
                      </div>
                      {result.redirectUrl && (
                        <div className="text-sm text-blue-600">
                          إعادة توجيه إلى: {result.redirectUrl}
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {getStatusBadge(result)}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => window.open(result.url, '_blank')}
                    >
                      <ExternalLink className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>

            {results.brokenLinks > 0 && (
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  تم العثور على {results.brokenLinks} رابط مكسور. يُنصح بإصلاح هذه الروابط لتحسين تجربة المستخدم و SEO.
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}

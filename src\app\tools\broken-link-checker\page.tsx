import { Metadata } from 'next';
import BrokenLinkChecker from '@/components/tools/BrokenLinkChecker';
import { getToolBySlug } from '@/lib/tools';
import { generateToolMetadata } from '@/lib/seo-utils';

const toolSlug = 'broken-link-checker';
const tool = getToolBySlug(toolSlug);

export const metadata: Metadata = generateToolMetadata({
  title: 'فاحص الروابط المكسورة - اكتشف الروابط التالفة في موقعك',
  description: 'أداة مجانية لفحص الروابط المكسورة في موقعك أو فحص قائمة من الروابط للتأكد من عملها. تحسين SEO وتجربة المستخدم.',
  keywords: [
    'فاحص الروابط المكسورة',
    'broken link checker',
    'فحص الروابط',
    'SEO',
    'تحسين المواقع',
    'روابط تالفة',
    'فحص الموقع',
    'أدوات المطورين',
    'تحليل الموقع',
    'جودة الموقع'
  ],
  toolSlug,
  category: 'أدوات المطورين'
});

export default function BrokenLinkCheckerPage() {
  if (!tool) {
    return <div>الأداة غير موجودة</div>;
  }

  return (
    <div className="container mx-auto py-8">
      <div className="mb-8 text-center">
        <h1 className="text-3xl font-bold mb-4">{tool.name}</h1>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          {tool.description}
        </p>
      </div>
      
      <BrokenLinkChecker />
      
      <div className="mt-12 space-y-8">
        <section className="prose prose-lg max-w-4xl mx-auto">
          <h2>ما هو فاحص الروابط المكسورة؟</h2>
          <p>
            فاحص الروابط المكسورة هو أداة مجانية تساعدك في اكتشاف الروابط التالفة أو المكسورة في موقعك الإلكتروني. 
            هذه الأداة ضرورية لضمان جودة موقعك وتحسين تجربة المستخدم ومحركات البحث.
          </p>

          <h3>لماذا يجب فحص الروابط المكسورة؟</h3>
          <ul>
            <li><strong>تحسين SEO:</strong> الروابط المكسورة تؤثر سلباً على ترتيب موقعك في محركات البحث</li>
            <li><strong>تجربة المستخدم:</strong> الروابط التالفة تخلق تجربة سيئة للزوار</li>
            <li><strong>المصداقية:</strong> المواقع التي تحتوي على روابط مكسورة تبدو غير محدثة</li>
            <li><strong>الفهرسة:</strong> محركات البحث قد تواجه صعوبة في فهرسة موقعك</li>
          </ul>

          <h3>كيفية استخدام الأداة</h3>
          <ol>
            <li><strong>فحص موقع واحد:</strong> أدخل رابط موقعك وسيتم فحص جميع الروابط الموجودة فيه</li>
            <li><strong>فحص متعدد:</strong> أدخل قائمة من الروابط (رابط واحد في كل سطر) لفحصها</li>
            <li><strong>مراجعة النتائج:</strong> ستحصل على تقرير مفصل يوضح حالة كل رابط</li>
            <li><strong>تصدير التقرير:</strong> يمكنك تصدير النتائج بصيغة CSV للمراجعة لاحقاً</li>
          </ol>

          <h3>أنواع المشاكل التي يمكن اكتشافها</h3>
          <ul>
            <li><strong>404 - صفحة غير موجودة:</strong> الرابط يشير إلى صفحة محذوفة</li>
            <li><strong>500 - خطأ في الخادم:</strong> مشكلة في الخادم المستضيف للصفحة</li>
            <li><strong>إعادة التوجيه:</strong> الروابط التي تعيد التوجيه إلى صفحات أخرى</li>
            <li><strong>انتهاء المهلة:</strong> الروابط التي تستغرق وقتاً طويلاً للاستجابة</li>
            <li><strong>أخطاء الشبكة:</strong> مشاكل في الاتصال بالخادم</li>
          </ul>

          <h3>نصائح لإصلاح الروابط المكسورة</h3>
          <ul>
            <li>قم بتحديث الروابط التي تشير إلى صفحات محذوفة</li>
            <li>أنشئ إعادة توجيه 301 للصفحات المنقولة</li>
            <li>احذف الروابط التي لم تعد ضرورية</li>
            <li>تحقق من الروابط الخارجية بانتظام</li>
            <li>استخدم أدوات مراقبة الموقع للفحص المستمر</li>
          </ul>

          <h3>أفضل الممارسات</h3>
          <ul>
            <li>افحص موقعك شهرياً على الأقل</li>
            <li>اختبر الروابط قبل نشر المحتوى الجديد</li>
            <li>احتفظ بنسخة احتياطية من موقعك قبل إجراء تغييرات كبيرة</li>
            <li>استخدم روابط نسبية للصفحات الداخلية عندما أمكن</li>
            <li>راقب تقارير Google Search Console للروابط المكسورة</li>
          </ul>
        </section>
      </div>
    </div>
  );
}

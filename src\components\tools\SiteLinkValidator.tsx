'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  Download,
  RefreshCw,
  Globe,
  ExternalLink
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { toolCategories, getAllTools } from '@/lib/tools';

interface LinkValidationResult {
  url: string;
  status: 'working' | 'broken' | 'redirect' | 'checking';
  statusCode?: number;
  responseTime?: number;
  error?: string;
  redirectUrl?: string;
}

export default function SiteLinkValidator() {
  const [isValidating, setIsValidating] = useState(false);
  const [progress, setProgress] = useState(0);
  const [results, setResults] = useState<LinkValidationResult[]>([]);
  const [summary, setSummary] = useState({
    total: 0,
    working: 0,
    broken: 0,
    redirects: 0
  });
  const { toast } = useToast();

  // Get all internal links from the site structure
  const getAllInternalLinks = () => {
    const baseUrl = window.location.origin;
    const links: string[] = [];

    // Add main pages
    links.push(`${baseUrl}/`);
    links.push(`${baseUrl}/articles`);
    links.push(`${baseUrl}/sitemap-page`);
    links.push(`${baseUrl}/disclaimer`);
    links.push(`${baseUrl}/p/about`);
    links.push(`${baseUrl}/p/contact`);
    links.push(`${baseUrl}/p/cookies`);
    links.push(`${baseUrl}/p/faq`);
    links.push(`${baseUrl}/p/guide`);

    // Add category pages
    toolCategories.forEach(category => {
      links.push(`${baseUrl}/categories/${category.slug}`);
    });

    // Add tool pages
    const allTools = getAllTools();
    allTools.forEach(tool => {
      links.push(`${baseUrl}${tool.path}`);
    });

    // Add some article pages
    const articleSlugs = [
      'how-to-calculate-zakat',
      'hijri-calendar-guide',
      'text-tools-productivity',
      'currency-converter-tips',
      'age-calculator-uses',
      'financial-planning-tools',
      'qr-code-complete-guide',
      'bmi-health-guide',
      'percentage-calculations-guide',
      'unit-conversion-mastery'
    ];

    articleSlugs.forEach(slug => {
      links.push(`${baseUrl}/articles/${slug}`);
    });

    // Add some year pages
    const currentYear = new Date().getFullYear();
    for (let year = currentYear - 2; year <= currentYear + 1; year++) {
      links.push(`${baseUrl}/year/${year}`);
    }

    return [...new Set(links)]; // Remove duplicates
  };

  const validateLink = async (url: string): Promise<LinkValidationResult> => {
    const startTime = Date.now();
    
    try {
      const response = await fetch(url, {
        method: 'HEAD',
        cache: 'no-cache'
      });
      
      const responseTime = Date.now() - startTime;
      
      if (response.ok) {
        return {
          url,
          status: 'working',
          statusCode: response.status,
          responseTime,
          redirectUrl: response.redirected ? response.url : undefined
        };
      } else if (response.status >= 300 && response.status < 400) {
        return {
          url,
          status: 'redirect',
          statusCode: response.status,
          responseTime,
          redirectUrl: response.url
        };
      } else {
        return {
          url,
          status: 'broken',
          statusCode: response.status,
          responseTime,
          error: `HTTP ${response.status} - ${response.statusText}`
        };
      }
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      return {
        url,
        status: 'broken',
        responseTime,
        error: error instanceof Error ? error.message : 'Network error'
      };
    }
  };

  const startValidation = async () => {
    setIsValidating(true);
    setProgress(0);
    setResults([]);
    
    const links = getAllInternalLinks();
    const totalLinks = links.length;
    
    // Initialize results with checking status
    const initialResults: LinkValidationResult[] = links.map(url => ({
      url,
      status: 'checking'
    }));
    setResults(initialResults);

    const validatedResults: LinkValidationResult[] = [];
    
    try {
      for (let i = 0; i < links.length; i++) {
        const link = links[i];
        setProgress(((i + 1) / totalLinks) * 100);
        
        const result = await validateLink(link);
        validatedResults.push(result);
        
        // Update results incrementally
        setResults(prev => 
          prev.map((item, index) => 
            index === i ? result : item
          )
        );
        
        // Small delay to avoid overwhelming the server
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // Calculate summary
      const working = validatedResults.filter(r => r.status === 'working').length;
      const broken = validatedResults.filter(r => r.status === 'broken').length;
      const redirects = validatedResults.filter(r => r.status === 'redirect').length;

      setSummary({
        total: totalLinks,
        working,
        broken,
        redirects
      });

      if (broken > 0) {
        toast({
          variant: 'destructive',
          title: 'تم العثور على روابط مكسورة',
          description: `تم العثور على ${broken} رابط مكسور`
        });
      } else {
        toast({
          title: 'ممتاز!',
          description: 'جميع الروابط تعمل بشكل صحيح!'
        });
      }

    } catch (error) {
      toast({
        variant: 'destructive',
        title: 'خطأ',
        description: 'حدث خطأ أثناء فحص الروابط'
      });
      console.error('Validation error:', error);
    } finally {
      setIsValidating(false);
    }
  };

  const exportResults = () => {
    const brokenLinks = results.filter(r => r.status === 'broken');
    
    if (brokenLinks.length === 0) {
      toast.info('لا توجد روابط مكسورة للتصدير');
      return;
    }

    const csvContent = [
      'URL,Status Code,Error,Response Time (ms)',
      ...brokenLinks.map(r => 
        `"${r.url}",${r.statusCode || 'N/A'},"${r.error || ''}",${r.responseTime || 'N/A'}`
      )
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = `broken-links-${new Date().toISOString().split('T')[0]}.csv`;
    link.click();
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'working':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'broken':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'redirect':
        return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
      case 'checking':
        return <RefreshCw className="w-4 h-4 text-blue-500 animate-spin" />;
      default:
        return null;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'working':
        return <Badge variant="default" className="bg-green-100 text-green-800">يعمل</Badge>;
      case 'broken':
        return <Badge variant="destructive">مكسور</Badge>;
      case 'redirect':
        return <Badge variant="secondary">إعادة توجيه</Badge>;
      case 'checking':
        return <Badge variant="outline">جاري الفحص...</Badge>;
      default:
        return null;
    }
  };

  return (
    <div className="w-full max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="w-5 h-5" />
            فحص روابط الموقع
          </CardTitle>
          <CardDescription>
            فحص جميع الروابط الداخلية في الموقع للتأكد من عملها بشكل صحيح
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button 
            onClick={startValidation} 
            disabled={isValidating}
            className="w-full"
          >
            {isValidating ? (
              <>
                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                جاري فحص الروابط...
              </>
            ) : (
              <>
                <Globe className="w-4 h-4 mr-2" />
                بدء فحص الروابط
              </>
            )}
          </Button>

          {isValidating && (
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>جاري الفحص...</span>
                <span>{Math.round(progress)}%</span>
              </div>
              <Progress value={progress} className="w-full" />
            </div>
          )}

          {results.length > 0 && (
            <>
              {/* Summary */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">{summary.total}</div>
                  <div className="text-sm text-blue-600">إجمالي الروابط</div>
                </div>
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">{summary.working}</div>
                  <div className="text-sm text-green-600">روابط تعمل</div>
                </div>
                <div className="text-center p-4 bg-red-50 rounded-lg">
                  <div className="text-2xl font-bold text-red-600">{summary.broken}</div>
                  <div className="text-sm text-red-600">روابط مكسورة</div>
                </div>
                <div className="text-center p-4 bg-yellow-50 rounded-lg">
                  <div className="text-2xl font-bold text-yellow-600">{summary.redirects}</div>
                  <div className="text-sm text-yellow-600">إعادة توجيه</div>
                </div>
              </div>

              {/* Export button */}
              {summary.broken > 0 && (
                <Button variant="outline" onClick={exportResults}>
                  <Download className="w-4 h-4 mr-2" />
                  تصدير الروابط المكسورة
                </Button>
              )}

              {/* Results list */}
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {results.map((result, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3 flex-1 min-w-0">
                      {getStatusIcon(result.status)}
                      <div className="flex-1 min-w-0">
                        <div className="font-medium truncate">{result.url}</div>
                        {result.error && (
                          <div className="text-sm text-red-600">{result.error}</div>
                        )}
                        {result.responseTime && (
                          <div className="text-sm text-muted-foreground">
                            وقت الاستجابة: {result.responseTime}ms
                          </div>
                        )}
                        {result.redirectUrl && (
                          <div className="text-sm text-blue-600">
                            إعادة توجيه إلى: {result.redirectUrl}
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {getStatusBadge(result.status)}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => window.open(result.url, '_blank')}
                      >
                        <ExternalLink className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>

              {summary.broken > 0 && (
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    تم العثور على {summary.broken} رابط مكسور. يُنصح بإصلاح هذه الروابط لتحسين تجربة المستخدم و SEO.
                  </AlertDescription>
                </Alert>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
